package repository

import (
	"archive/zip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	v1 "esop/api/v1"
	"esop/internal/model"

	// 导入 filepath 包

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TemplateRepository interface {
	Create(ctx context.Context, v *v1.SaveTemplateListRequest) error
	AddPack(ctx *gin.Context, v *v1.SavePackRequest) error
	FirstById(id int64) (*model.Template, error)
	GetTemplateList(context.Context, v1.TemplateListRequest) (int64, []*v1.TemplateData, error)
	Delete(ctx context.Context, id int) error
	GetTemplateDetail(ctx context.Context, id int) (v1.TemplateData, error)
	Edit(ctx context.Context, id int, req v1.SaveTemplateListRequest) error
}

func NewTemplateRepository(repository *Repository) TemplateRepository {
	return &templateRepository{
		Repository: repository,
		// OperationLogRepository: NewOperationLogRepository(repository),
		ResourcePackRepository: NewResourcePackRepository(repository),
	}
}

type templateRepository struct {
	*Repository
	ResourcePackRepository ResourcePackRepository
}

func (r *templateRepository) FirstById(id int64) (*model.Template, error) {
	var template model.Template
	// TODO: query db
	return &template, nil
}

func (r *templateRepository) GetTemplateList(ctx context.Context, request v1.TemplateListRequest) (int64, []*v1.TemplateData, error) {
	var (
		templateList []*v1.TemplateData
		total        int64 = 10
		err          error
	)
	db := r.DB(ctx).Table("template").
		Where("is_deleted = 1").
		Order("id DESC").
		Select("*").
		Count(&total)

	if request.Name != "" {
		db.Where("name like ?", "%"+request.Name+"%")
	}

	if request.CreatedAtStart != 0 {
		db.Where("created_at >=  ?", request.CreatedAtStart)
	}
	if request.CreatedAtEnd != 0 {
		db.Where("created_at <= ?", request.CreatedAtEnd)
	}
	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&templateList).Error
	if err != nil {
		return total, nil, err
	}
	return total, templateList, nil
}

func (r *templateRepository) Delete(ctx context.Context, id int) error {
	now := time.Now().Unix()
	update := &model.Template{
		IsDeleted: 2,
		UpdatedAt: now,
	}
	if err := r.DB(ctx).Table("template").Where("id = ?", id).Updates(update).Error; err != nil {
		return err
	}
	return nil
}

func (r *templateRepository) Create(ctx context.Context, v *v1.SaveTemplateListRequest) error {
	now := time.Now().Unix()
	Template := &model.Template{
		Name:            v.Name,
		ResolutionRatio: v.ResolutionRatio,
		CanvasRatio:     v.CanvasRatio,
		SwipterTime:     v.SwipterTime,
		Type:            v.Type,
		IsDeleted:       1,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
	// 新增模板主表
	if err := r.DB(ctx).Create(Template).Error; err != nil {
		return err
	}
	// 新增模板副表
	var templateSmData []*model.TemplateSm
	for _, sm := range v.TemplateSm {
		// 序列化MultiFiles为JSON字符串
		multiFilesJSON, err := json.Marshal(sm.MultiFiles)
		if err != nil {
			return err
		}

		smData := &model.TemplateSm{
			TemplateId:        Template.ID,
			SmId:              sm.SmId,
			Width:             sm.Width,
			Height:            sm.Height,
			SourceWidth:       sm.SourceWidth,
			SourceHeight:      sm.SourceHeight,
			XAxis:             sm.XAxis,
			Url:               sm.Path, // 使用Path作为Url
			YAxis:             sm.YAxis,
			SmName:            sm.SmName,
			TemplatePage:      int8(sm.TemplatePage),   // 类型转换
			TemplateSmType:    int8(sm.TemplateSmType), // 类型转换
			TemplateIndex:     sm.TemplateIndex,
			BackgroundDisplay: sm.BackgroundDisplay,
			IntervalTime:      0,                      // 默认值，后续可根据multiFiles计算
			MultiFiles:        string(multiFilesJSON), // 转换为字符串存储
		}
		templateSmData = append(templateSmData, smData)
	}

	// Step 3: Batch insert TemplateSmData records
	if err := r.DB(ctx).Create(&templateSmData).Error; err != nil {
		return err
	}
	return nil
}

func (r *templateRepository) GetTemplateDetail(ctx context.Context, id int) (v1.TemplateData, error) {
	var list v1.TemplateData
	if err := r.DB(ctx).Table("template").
		Select("*").
		Where(&model.Template{ID: id, IsDeleted: 1}).First(&list).Error; err != nil {
		return list, err
	}
	// 查询模板关联的素材数据
	TemplateSmDetail, err := r.GetTemplateSmData(ctx, id)
	if err != nil {
		return list, err
	}

	// 填充 TemplateData 结构体中的 TemplateSmData 字段
	list.TemplateSmDetail = TemplateSmDetail
	return list, nil
}

func (r *templateRepository) GetTemplateSmData(ctx context.Context, templateID int) ([]v1.TemplateSmDetail, error) {
	var TemplateSmDetail []v1.TemplateSmDetail

	// 查询 template_sm 表中关联的素材数据
	if err := r.DB(ctx).Table("template_sm").
		Joins("LEFT JOIN source_material ON source_material.id = template_sm.sm_id").
		Select("template_sm.*, source_material.path").
		Where("template_id = ?", templateID).
		Find(&TemplateSmDetail).Error; err != nil {
		return nil, err
	}

	// 解析 MultiFilesStr 为 MultiFiles 数组
	for i := range TemplateSmDetail {
		if TemplateSmDetail[i].MultiFilesStr != "" {
			var multiFiles []v1.MultiFiles
			if err := json.Unmarshal([]byte(TemplateSmDetail[i].MultiFilesStr), &multiFiles); err != nil {
				r.logger.Error("解析 MultiFiles 失败", zap.Error(err), zap.Int("templateID", templateID))
				TemplateSmDetail[i].MultiFiles = []v1.MultiFiles{}
			} else {
				TemplateSmDetail[i].MultiFiles = multiFiles
			}
		} else {
			TemplateSmDetail[i].MultiFiles = []v1.MultiFiles{}
		}
	}

	return TemplateSmDetail, nil
}

func (r *templateRepository) Edit(ctx context.Context, id int, req v1.SaveTemplateListRequest) error {
	Template := &model.Template{
		Name:            req.Name,
		ResolutionRatio: req.ResolutionRatio,
		CanvasRatio:     req.CanvasRatio,
		SwipterTime:     req.SwipterTime,
		UpdatedAt:       time.Now().Unix(),
	}

	if err := r.DB(ctx).Table("template").Where("id = ?", id).Updates(Template).Error; err != nil {
		return err
	}

	// 删除旧的附表关联数据
	if err := r.DeleteTemplateSmData(ctx, id); err != nil {
		return err
	}
	// fmt.Println(req.TemplateSm)
	// 新增新的附表关联数据
	for _, smData := range req.TemplateSm {
		if err := r.CreateTemplateSmData(ctx, id, smData); err != nil {
			return err
		}
	}
	return nil
}

// DeleteTemplateSm 删除指定模板ID的所有附表关联数据
func (r *templateRepository) DeleteTemplateSmData(ctx context.Context, templateID int) error {
	if err := r.DB(ctx).Table("template_sm").Where("template_id = ?", templateID).Delete(&model.TemplateSm{}).Error; err != nil {
		return err
	}
	return nil
}

// CreateTemplateSmData 创建新的附表关联数据
func (r *templateRepository) CreateTemplateSmData(ctx context.Context, templateId int, smData v1.TemplateSm) error {
	// 序列化MultiFiles为JSON字符串
	multiFilesJSON, err := json.Marshal(smData.MultiFiles)
	if err != nil {
		return err
	}

	modelData := &model.TemplateSm{
		TemplateId:        templateId,
		SmId:              smData.SmId,
		Type:              smData.Type,
		Width:             smData.Width,
		Height:            smData.Height,
		SourceWidth:       smData.SourceWidth,
		SourceHeight:      smData.SourceHeight,
		XAxis:             smData.XAxis,
		Url:               smData.Path,
		YAxis:             smData.YAxis,
		SmName:            smData.SmName,
		TemplatePage:      int8(smData.TemplatePage),
		TemplateSmType:    int8(smData.TemplateSmType),
		TemplateIndex:     smData.TemplateIndex,
		BackgroundDisplay: smData.BackgroundDisplay,
		IntervalTime:      0, // 默认值
		MultiFiles:        string(multiFilesJSON),
	}

	if err := r.DB(ctx).Table("template_sm").Create(modelData).Error; err != nil {
		return err
	}
	return nil
}

// AddPack 处理保存资源包的请求
func (r *templateRepository) AddPack(ctx *gin.Context, v *v1.SavePackRequest) error {
	fmt.Println(v)

	// 获取当前工作目录
	dir, err := os.Getwd()
	if err != nil {
		return r.handleError(ctx, err)
	}

	// 定义 zippack 和 resource 目录路径
	assetsBigDir := filepath.Join(dir, "assets")
	zippackDir := filepath.Join(assetsBigDir, "zippack")
	resourceDir := filepath.Join(zippackDir, "resource")
	assetsDir := filepath.Join(resourceDir, "assets")
	staticDir := filepath.Join(assetsBigDir, "static")
	// 创建 assets 目录
	if err := r.createDirectory(assetsBigDir); err != nil {
		return r.handleError(ctx, err)
	}

	// 创建 zippack 目录
	if err := r.createDirectory(zippackDir); err != nil {
		return r.handleError(ctx, err)
	}

	// 创建 resource 目录
	if err := r.createDirectory(resourceDir); err != nil {
		return r.handleError(ctx, err)
	}
	// 类型为1才可以新建html文件

	if v.Type != 2 {
		// 创建 template.html 文件
		if err := r.createFile(filepath.Join(resourceDir, "index.html"), v.HtmlContent); err != nil {
			return r.handleError(ctx, err)
		}
		jsonData, err := r.GetTemplateDetail(ctx, v.Id)
		if err != nil {
			return r.handleError(ctx, err)
		}
		jsonBytes, err := json.Marshal(jsonData)
		if err != nil {
			return r.handleError(ctx, err)
		}
		if err := r.createFile(filepath.Join(resourceDir, "data.json"), string(jsonBytes)); err != nil {
			return r.handleError(ctx, err)
		}
	}

	// 创建 assets 目录
	if err := r.createAssetsDirectory(resourceDir); err != nil {
		return r.handleError(ctx, err)
	}

	if err := r.copyFile(filepath.Join(staticDir, "jquery.min.js"), filepath.Join(assetsDir, "jquery.min.js")); err != nil {
		return r.handleError(ctx, err)
	}
	if err := r.copyFile(filepath.Join(staticDir, "swiper.min.js"), filepath.Join(assetsDir, "swiper.min.js")); err != nil {
		return r.handleError(ctx, err)
	}
	if err := r.copyFile(filepath.Join(staticDir, "swiper.min.css"), filepath.Join(assetsDir, "swiper.min.css")); err != nil {
		return r.handleError(ctx, err)
	}

	// 复制文件到目录中
	var sourceMaterialData []v1.TemplateSmDetail
	// 执行查询并检查错误
	if err := r.DB(ctx).Table("template_sm").
		Joins("LEFT JOIN source_material ON source_material.id = template_sm.sm_id").
		Select("template_sm.*, source_material.path").
		Where("template_sm.template_id = ?", v.Id).
		Find(&sourceMaterialData).Error; err != nil {
		return err
	}

	for _, sm := range sourceMaterialData {
		// 解析MultiFiles
		var multiFiles []v1.MultiFiles
		if sm.MultiFilesStr != "" {
			if err := json.Unmarshal([]byte(sm.MultiFilesStr), &multiFiles); err != nil {
				r.logger.Warn("解析MultiFiles失败", zap.Error(err))
				// 继续处理下一个素材
				continue
			}
		}

		// 收集所有需要复制的文件路径
		filesToCopy := []string{}
		if sm.Path != "" {
			filesToCopy = append(filesToCopy, sm.Path)
		}
		for _, mf := range multiFiles {
			if mf.Path != "" {
				filesToCopy = append(filesToCopy, mf.Path)
			}
		}

		// 复制文件
		for _, path := range filesToCopy {
			sourcePath := filepath.Join(dir, "assets", "media", path)
			destPath := filepath.Join(assetsDir, path)

			// 检查目标路径是否已存在
			if _, err := os.Stat(destPath); err == nil {
				r.logger.Info("文件已存在，跳过", zap.String("file", path))
				continue
			} else if !os.IsNotExist(err) {
				r.logger.Error("检查文件是否存在时出错", zap.String("file", path), zap.Error(err))
				return err
			}

			// 复制文件到目标目录
			if err := r.copyFile(sourcePath, destPath); err != nil {
				r.logger.Error("复制文件失败", zap.String("source", sourcePath), zap.String("destination", destPath), zap.Error(err))
				return err
			}
		}
	}

	// 压缩 resource 目录成 zip 文件
	zipFile := filepath.Join(zippackDir, v.ResourcePackName+".zip")
	if err := r.zipDirectory(resourceDir, zipFile); err != nil {
		return r.handleError(ctx, err)
	}

	// 删除 resource 目录
	if err := r.removeDirectory(resourceDir); err != nil {
		return r.handleError(ctx, err)
	}

	// 新增资源包
	now := time.Now().Unix()
	ResourcePackData := &model.ResourcePack{
		Name:       v.Name,
		TemplateId: v.Id,
		IsDeleted:  1,
		PackName:   v.ResourcePackName,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	if err := r.ResourcePackRepository.Create(ctx, ResourcePackData); err != nil {
		return r.handleError(ctx, err)
	}

	return nil
}

// 复制文件的辅助函数
func (r *templateRepository) copyFile(sourcePath, destPath string) error {
	fmt.Println(sourcePath, "sourcePath, destPath sourcePath, destPath sourcePath, destPath ", destPath)
	sourceFile, err := os.Open(sourcePath)
	if err != nil {
		return fmt.Errorf("无法打开源文件 %s: %w", sourcePath, err)
	}
	defer sourceFile.Close()

	destFile, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("无法创建目标文件 %s: %w", destPath, err)
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("复制文件内容失败: %w", err)
	}

	return nil
}

// handleError 记录错误日志并返回错误响应
func (r *templateRepository) handleError(ctx *gin.Context, err error) error {
	r.logger.Error("操作失败", zap.Error(err))
	v1.HandleError(ctx, http.StatusOK, err, nil)
	return err
}

// createDirectory 如果目录不存在则创建它
func (r *templateRepository) createDirectory(dir string) error {
	// 检查目录是否存在
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		// 创建目录
		err := os.MkdirAll(dir, 0o755)
		if err != nil {
			r.logger.Error("创建目录失败", zap.String("directory", dir), zap.Error(err))
			return err
		}
		r.logger.Info("目录创建成功", zap.String("directory", dir))
	}
	return nil
}

// createFile 创建一个文件并写入内容
func (r *templateRepository) createFile(filePath, content string) error {
	// 写入文件
	err := os.WriteFile(filePath, []byte(content), 0o644)
	if err != nil {
		r.logger.Error("写入文件失败", zap.String("filePath", filePath), zap.Error(err))
		return err
	}
	r.logger.Info("文件创建成功", zap.String("filePath", filePath))
	return nil
}

// createAssetsDirectory 创建 assets 目录
func (r *templateRepository) createAssetsDirectory(resourceDir string) error {
	assetsDir := filepath.Join(resourceDir, "assets")
	return r.createDirectory(assetsDir)
}

// removeDirectory 删除指定目录及其内容
func (r *templateRepository) removeDirectory(dir string) error {
	// 删除目录及其内容
	err := os.RemoveAll(dir)
	if err != nil {
		r.logger.Error("删除目录失败", zap.String("directory", dir), zap.Error(err))
		return err
	}
	r.logger.Info("删除目录成功", zap.String("directory", dir))
	return nil
}

// zipDirectory 压缩指定目录成 zip 文件
func (r *templateRepository) zipDirectory(sourceDir, zipFile string) error {
	// 创建 zip 文件
	zipFileHandle, err := os.Create(zipFile)
	if err != nil {
		return err
	}
	defer zipFileHandle.Close()

	// 创建 zip 写入器
	zipWriter := zip.NewWriter(zipFileHandle)
	defer zipWriter.Close()

	// 遍历目录并写入 zip 文件
	err = filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}

		// 获取相对路径
		relPath, err := filepath.Rel(sourceDir, path)
		if err != nil {
			return err
		}

		// 创建 zip 条目
		zipEntry, err := zipWriter.Create(relPath)
		if err != nil {
			return err
		}

		// 复制文件内容到 zip 条目
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(zipEntry, file)
		return err
	})
	return err
}
