package model

// 说明:模板素材关联表
// 表名:template
// version:2024-07-4
type TemplateSm struct {
	Id                int     `gorm:"column:id;primaryKey" json:"id"`                      // 编号
	TemplateIndex     int     `gorm:"column:template_index" json:"template_index"`         // 素材排序
	Type              int     `gorm:"column:type" json:"type"`                             // 素材类型
	TemplateId        int     `gorm:"column:template_id" json:"template_id"`               // 模板Id
	SmId              int     `gorm:"column:sm_id" json:"sm_id"`                           // 素材id
	Width             float64 `gorm:"column:width" json:"width"`                           // 素材宽度
	Height            float64 `gorm:"column:height" json:"height"`                         // 素材高度
	SourceWidth       float64 `gorm:"column:source_width" json:"source_width"`             // 素材源宽度
	SourceHeight      float64 `gorm:"column:source_height" json:"source_height"`           // 素材源高度
	Url               string  `gorm:"column:url" json:"url"`                               // 素材地址
	XAxis             int     `gorm:"column:x_axis" json:"x_axis"`                         // X轴
	YAxis             int     `gorm:"column:y_axis" json:"y_axis"`                         // y轴
	SmName            string  `gorm:"column:sm_name" json:"sm_name"`                       // 素材名称
	CreatedAt         int64   `gorm:"column:created_at;autoCreateTime" json:"created_at"`  // 创建时间
	UpdatedAt         int64   `gorm:"column:updated_at;autoUpdatedTime" json:"updated_at"` // 更新时间
	TemplateSmType    int8    `gorm:"template_sm_type" json:"template_sm_type"`            // 关联表类型 1---素材表数据 2--时间  3--
	TemplatePage      int8    `gorm:"template_page" json:"template_page"`                  // 分页类型
	BackgroundDisplay string  `gorm:"background_display" json:"background_display"`        // 背景图拉伸类型
	IntervalTime      int     `gorm:"column:interval_time" json:"interval_time"`           // 轮播图间隔时间
	MultiFiles        string  `gorm:"column:multi_files;type:json" json:"multi_files"`     // 多文件列表
	// IsDeleted  int8   `gorm:"column:is_deleted" json:"is_deleted"`   // 是否删除
}

func (m *TemplateSm) TableName() string {
	return "template_sm"
}
