<template>
    <el-dialog title="分组管理" :visible.sync="visible" width="600px" :close-on-click-modal="false"
        :close-on-press-escape="false" @close="handleClose">
        <div class="group-management">
            <!-- 新增分组 -->
            <div class="add-group-section">
                <el-form :model="newGroupForm" :rules="groupRules" ref="newGroupForm" inline>
                    <el-form-item prop="name">
                        <el-input v-model="newGroupForm.name" placeholder="请输入分组名称" style="width: 200px;">
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="description">
                        <el-input v-model="newGroupForm.description" placeholder="分组描述（可选）" style="width: 250px;">
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="createGroup" :loading="creating">
                            <i class="el-icon-plus"></i> 新增分组
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 分组列表 -->
            <div class="group-list-section">
                <div class="section-header">
                    <span>现有分组</span>
                    <el-button type="text" @click="loadGroupList" :loading="loading">
                        <i class="el-icon-refresh"></i> 刷新
                    </el-button>
                </div>

                <div class="group-list" v-loading="loading">
                    <div v-if="groupList.length === 0" class="empty-state">
                        <i class="el-icon-folder-opened"></i>
                        <p>暂无分组，请先创建分组</p>
                    </div>

                    <div v-else class="group-items">
                        <div v-for="group in groupList" :key="group.id" class="group-item">
                            <div class="group-info">
                                <div class="group-name">
                                    <i class="el-icon-folder"></i>
                                    {{ group.name }}
                                </div>
                                <div class="group-description" v-if="group.description">
                                    {{ group.description }}
                                </div>
                                <div class="group-meta">
                                    <span class="create-time">创建时间: {{ formatTime(group.created_at) }}</span>
                                </div>
                            </div>
                            <div class="group-actions">
                                <el-button type="text" @click="editGroup(group)" size="small">
                                    <i class="el-icon-edit"></i> 编辑
                                </el-button>
                                <el-button type="text" @click="deleteGroup(group)" size="small" class="delete-btn">
                                    <i class="el-icon-delete"></i> 删除
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关闭</el-button>
        </div>

        <!-- 编辑分组弹窗 -->
        <el-dialog title="编辑分组" :visible.sync="editDialogVisible" width="400px" append-to-body>
            <el-form :model="editGroupForm" :rules="groupRules" ref="editGroupForm">
                <el-form-item label="分组名称" prop="name">
                    <el-input v-model="editGroupForm.name" placeholder="请输入分组名称"></el-input>
                </el-form-item>
                <el-form-item label="分组描述" prop="description">
                    <el-input v-model="editGroupForm.description" placeholder="分组描述（可选）" type="textarea" rows="3">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="updateGroup" :loading="updating">确定</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import { getAllMaterialGroups, createMaterialGroup, editMaterialGroup, deleteMaterialGroup } from '@/api/material'

export default {
    name: 'MaterialGroupDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            loading: false,
            creating: false,
            updating: false,
            groupList: [],
            newGroupForm: {
                name: '',
                description: ''
            },
            editGroupForm: {
                id: null,
                name: '',
                description: ''
            },
            editDialogVisible: false,
            groupRules: {
                name: [
                    { required: true, message: '请输入分组名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '分组名称长度在 1 到 50 个字符', trigger: 'blur' }
                ],
                description: [
                    { max: 200, message: '分组描述不能超过 200 个字符', trigger: 'blur' }
                ]
            }
        }
    },
    watch: {
        visible (newVal) {
            if (newVal) {
                this.loadGroupList()
            }
        }
    },
    methods: {
        // 加载分组列表
        async loadGroupList () {
            this.loading = true
            try {
                const response = await getAllMaterialGroups()
                if (response.code === 0) {
                    this.groupList = response.data || []
                }
            } catch (error) {
                console.error('获取分组列表失败:', error)
                this.$message.error('获取分组列表失败')
            } finally {
                this.loading = false
            }
        },

        // 创建分组
        async createGroup () {
            this.$refs.newGroupForm.validate(async (valid) => {
                if (!valid) return

                this.creating = true
                try {
                    const response = await createMaterialGroup(this.newGroupForm)
                    if (response.code === 0) {
                        this.$message.success('分组创建成功')
                        this.resetNewGroupForm()
                        this.loadGroupList()
                        // 通知父组件刷新分组列表
                        this.$emit('group-changed')
                    } else {
                        this.$message.error(response.msg || '创建分组失败')
                    }
                } catch (error) {
                    console.error('创建分组失败:', error)
                    this.$message.error('创建分组失败')
                } finally {
                    this.creating = false
                }
            })
        },

        // 编辑分组
        editGroup (group) {
            this.editGroupForm = {
                id: group.id,
                name: group.name,
                description: group.description || ''
            }
            this.editDialogVisible = true
        },

        // 更新分组
        async updateGroup () {
            this.$refs.editGroupForm.validate(async (valid) => {
                if (!valid) return

                this.updating = true
                try {
                    const { id, ...updateData } = this.editGroupForm
                    const response = await editMaterialGroup(id, updateData)
                    if (response.code === 0) {
                        this.$message.success('分组更新成功')
                        this.editDialogVisible = false
                        this.loadGroupList()
                        // 通知父组件刷新分组列表
                        this.$emit('group-changed')
                    } else {
                        this.$message.error(response.msg || '更新分组失败')
                    }
                } catch (error) {
                    console.error('更新分组失败:', error)
                    this.$message.error('更新分组失败')
                } finally {
                    this.updating = false
                }
            })
        },

        // 删除分组
        deleteGroup (group) {
            this.$confirm(`确定要删除分组"${group.name}"吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await deleteMaterialGroup(group.id)
                    if (response.code === 0) {
                        this.$message.success('分组删除成功')
                        this.loadGroupList()
                        // 通知父组件刷新分组列表
                        this.$emit('group-changed')
                    } else {
                        this.$message.error(response.msg || '删除分组失败')
                    }
                } catch (error) {
                    console.error('删除分组失败:', error)
                    this.$message.error('删除分组失败')
                }
            }).catch(() => {
                // 用户取消删除
            })
        },

        // 重置新增分组表单
        resetNewGroupForm () {
            this.newGroupForm = {
                name: '',
                description: ''
            }
            this.$refs.newGroupForm.resetFields()
        },

        // 格式化时间
        formatTime (timestamp) {
            if (!timestamp) return '-'
            const date = new Date(timestamp * 1000)
            return date.toLocaleString('zh-CN')
        },

        // 关闭弹窗
        handleClose () {
            this.resetNewGroupForm()
            this.editDialogVisible = false
            this.$emit('update:visible', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.group-management {
    .add-group-section {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 20px;

        .el-form {
            margin-bottom: 0;
        }
    }

    .group-list-section {
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ebeef5;
            margin-bottom: 15px;

            span {
                font-weight: 500;
                color: #303133;
            }
        }

        .group-list {
            min-height: 200px;

            .empty-state {
                text-align: center;
                padding: 40px 0;
                color: #909399;

                i {
                    font-size: 48px;
                    margin-bottom: 16px;
                    display: block;
                }

                p {
                    margin: 0;
                    font-size: 14px;
                }
            }

            .group-items {
                .group-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px;
                    border: 1px solid #ebeef5;
                    border-radius: 4px;
                    margin-bottom: 10px;
                    transition: all 0.3s;

                    &:hover {
                        border-color: #409EFF;
                        background: #f0f9ff;
                    }

                    .group-info {
                        flex: 1;

                        .group-name {
                            font-weight: 500;
                            color: #303133;
                            margin-bottom: 5px;

                            i {
                                color: #409EFF;
                                margin-right: 8px;
                            }
                        }

                        .group-description {
                            color: #606266;
                            font-size: 13px;
                            margin-bottom: 5px;
                        }

                        .group-meta {
                            font-size: 12px;
                            color: #909399;

                            .create-time {
                                margin-right: 15px;
                            }
                        }
                    }

                    .group-actions {
                        display: flex;
                        gap: 5px;

                        .delete-btn {
                            color: #f56c6c;

                            &:hover {
                                background: #fef0f0;
                            }
                        }
                    }
                }
            }
        }
    }
}

.dialog-footer {
    text-align: right;
}
</style>