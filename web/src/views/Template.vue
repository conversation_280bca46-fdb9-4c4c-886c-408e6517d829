<template>
  <div class="template-page">
    <el-container class="template-layout">
      <!-- 主内容区 -->
      <el-main class="content-main">
        <!-- 顶部操作栏 -->
        <div class="action-bar">
          <div class="actions-left">
            <el-button type="primary" icon="el-icon-plus" @click="add()">创建模板</el-button>
            <!-- <el-button type="success" icon="el-icon-plus" @click="addwork()">创建工作模板</el-button> -->
          </div>
          <div class="actions-right">
            <el-input v-model="form.name" placeholder="按名称搜索" clearable @clear="getList"
              @keyup.enter.native="searchForm" class="search-input">
              <el-button slot="append" icon="el-icon-search" @click="searchForm"></el-button>
            </el-input>
            <el-date-picker v-model="form.date" type="daterange" :range-separator="$t('public.to')"
              :start-placeholder="$t('public.startDate')" :end-placeholder="$t('public.endDate')" size="small">
            </el-date-picker>
            <el-button @click="resetForm()" size="small">重置</el-button>
          </div>
        </div>

        <!-- 模板展示区 -->
        <div v-loading="isLoading" class="template-grid-container">
          <el-row :gutter="20">
            <el-col v-for="item in dataList" :key="item.id" :xs="12" :sm="8" :md="6" :lg="4" :xl="4">
              <el-card shadow="hover" class="template-card">
                <div class="card-preview">
                  <div class="template-icon">
                    <i :class="item.type === 1 ? 'el-icon-document' : 'el-icon-s-cooperation'"></i>
                  </div>
                </div>
                <div class="card-info">
                  <p class="card-name" :title="item.name">{{ item.name }}</p>
                  <div class="card-details">
                    <span :class="['type-tag', `type-${item.type}`]">
                      {{ item.type === 1 ? '普通模板' : '工作模板' }}
                    </span>
                    <span v-if="item.resolution_ratio">{{ item.resolution_ratio }}</span>
                  </div>
                  <time class="card-time">{{ $formatTimeStamp(item.created_at, "YYYY-MM-DD HH:mm") }}</time>
                </div>
                <div class="card-actions">
                  <el-dropdown trigger="click" @command="handleCommand($event, item)">
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="edit" icon="el-icon-edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="pack" icon="el-icon-box">打包</el-dropdown-item>
                      <el-dropdown-item command="delete" icon="el-icon-delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-empty v-if="!isLoading && dataList.length === 0" description="暂无模板"></el-empty>
        </div>

        <!-- 分页 -->
        <el-pagination v-if="total > 0" background layout="total, sizes, prev, pager, next, jumper" :total="total"
          :page-size.sync="pageSize" :current-page.sync="pageNum" @size-change="getList" @current-change="getList"
          class="pagination-footer">
        </el-pagination>
      </el-main>
    </el-container>
    <el-dialog :title="isEdit ? '编辑模板' : '创建模板'" :visible.sync="isShowTemplate" fullscreen @close="close"
      :close-on-click-modal="false" :close-on-press-escape="false" class="template-dialog">
      <!-- 顶部工具栏 -->
      <div class="template-toolbar">
        <!-- 页面管理 -->
        <div class="tool-group">

          <div class="tool-controls">
            <el-button type="primary" icon="el-icon-plus" @click="addNewPage()" size="small">新增页面</el-button>
            <el-button @click="prevPage()" :disabled="currentPage === 0" size="small"
              icon="el-icon-arrow-left">上一页</el-button>
            <div class="page-info">
              <span>第 {{ currentPage + 1 }} / {{ templatePages.length }} 页</span>
            </div>
            <el-button @click="nextPage()" :disabled="currentPage === templatePages.length - 1" size="small">
              下一页<i class="el-icon-arrow-right el-icon--right"></i>
            </el-button>
            <el-button @click="delPage()" :disabled="templatePages.length === 1" size="small" type="danger"
              icon="el-icon-delete" plain>删除页面</el-button>
          </div>
        </div>

        <!-- 内容添加 -->
        <div class="tool-group">

          <div class="area-buttons">
            <el-tooltip content="点击添加图片区域" placement="top">
              <el-button type="success" @click="addMaterial(1)" icon="el-icon-picture-outline" size="small"
                class="area-btn">
                图片
              </el-button>
            </el-tooltip>
            <el-tooltip content="点击添加视频区域" placement="top">
              <el-button type="warning" @click="addMaterial(2)" icon="el-icon-video-camera" size="small"
                class="area-btn">
                视频
              </el-button>
            </el-tooltip>
            <el-tooltip content="点击添加时间组件" placement="top">
              <el-button type="info" @click="addMaterial(3)" icon="el-icon-time" size="small" class="area-btn">
                时间
              </el-button>
            </el-tooltip>
            <el-tooltip content="点击添加网页区域" placement="top">
              <el-button type="default" @click="addMaterial(5)" icon="el-icon-link" size="small" class="area-btn">
                网页
              </el-button>
            </el-tooltip>
          </div>

        </div>

        <!-- 画布设置 -->
        <div class="tool-group">

          <div class="tool-controls">
            <el-button type="primary" @click="addMaterial(4)" icon="el-icon-picture" size="small" plain>设置背景</el-button>
            <el-select v-model="templatePages[currentPage].backgroundSettings.backgroundSize" placeholder="背景显示方式"
              size="small" style="width: 120px;">
              <el-option label="重复" value="repeat"></el-option>
              <el-option label="覆盖" value="cover"></el-option>
              <el-option label="包含" value="contain"></el-option>
              <el-option label="自动" value="auto"></el-option>
            </el-select>
            <el-button v-if="templatePages[currentPage].backgroundUrl" type="danger" @click="clearBackground()"
              size="small" plain>清除背景</el-button>
            <el-button type="info" @click="clearTemplate" size="small" plain icon="el-icon-refresh">重置当前页</el-button>
          </div>
        </div>
      </div>
      <div class="container">
        <!-- 左侧面板：公共素材面板 -->
        <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
          <common-materials-panel />
        </div>

        <!-- 左侧分割线 -->
        <resizable-splitter :min-width="leftPanelMinWidth" :max-width="leftPanelMaxWidth"
          :initial-width="leftPanelWidth" @dragging="handleLeftPanelDrag" @drag-start="handleLeftPanelDragStart"
          @drag-end="handleLeftPanelDragEnd" />

        <!-- 中间面板：模板区域 -->
        <div class="middle-pane" ref="middlePane">
          <template-area ref="templateArea" :material-list="templatePages[currentPage].materialList"
            :highlighted-index="highlightedMaterialIndex" :image-url="imageUrl" :area-type-configs="areaTypeConfigs"
            :canvas-width="canvasDimensions.width" :canvas-height="canvasDimensions.height"
            :background-url="templatePages[currentPage].backgroundUrl"
            :background-settings="templatePages[currentPage].backgroundSettings" @dragging="handleDragging"
            @drag-start="handleDragStart" @drag-end="handleDragEnd" @resizing="handleResizing"
            @resize-start="handleResizeStart" @resize-end="handleResizeEnd" @dragover="handleDragOver"
            @drop="handleDrop" @delete-material="delMaterial" @is-drop-target="isDropTarget"
            @area-click="handleAreaClick" @canvas-click="handleCanvasClick"
            :key="`template-area-${currentPage}-${templateAreaRefreshKey}-${sharedAddForm.resolution_ratio}`" />
        </div>

        <!-- 右侧分割线 -->
        <resizable-splitter :min-width="rightPanelMinWidth" :max-width="rightPanelMaxWidth"
          :initial-width="rightPanelWidth" direction="left" @dragging="handleRightPanelDrag"
          @drag-start="handleRightPanelDragStart" @drag-end="handleRightPanelDragEnd" />

        <!-- 右侧面板：属性面板 -->
        <div class="right-panel" :style="{ width: rightPanelWidth + 'px' }">
          <template-properties-panel :shared-add-form.sync="sharedAddForm" :rules="rules"
            :resolution-ratio-list="resolutionRatioList"
            :current-page-materials="templatePages[currentPage].materialList"
            :selected-material="getCurrentHighlightedMaterial()" :selected-material-index="highlightedMaterialIndex"
            @delete-material="delMaterial" @move-layer="moveLayer" @highlight-material="highlightMaterial"
            @update-interval-time="updateIntervalTime" @materials-reordered="onMaterialsReordered"
            @reset-carousel-index="resetCarouselIndex" @update-carousel-interval="updateCarouselInterval"
            @update-multi-files="handleUpdateMultiFiles" ref="propertiesPanel" />
        </div>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowTemplate = false">{{
          $t("public.cancel")
          }}</el-button>
        <el-button type="primary" @click="save()">{{
          $t("public.confirm")
          }}</el-button>
      </span>
    </el-dialog>
    <!-- 选择素材图片弹窗 -->
    <el-dialog :title="isMultiSelect ?
      (type === 1 ? '选择图片（支持多选）' : type === 2 ? '选择视频（支持多选）' : '添加素材') :
      '添加素材'" :append-to-body="true" style="margin-top: 100px" :visible.sync="isShowMaterial" width="80%"
      @close="close1">
      <el-container style="height: 500px;">
        <!-- 左侧分组栏 -->
        <el-aside width="200px" style="border-right: 1px solid #e6e6e6; padding-right:10px">
          <el-menu :default-active="String(selectedGroupId)" @select="handleGroupSelect">
            <el-menu-item index="0">
              <i class="el-icon-files"></i>
              <span slot="title">全部分组</span>
            </el-menu-item>
            <el-menu-item v-for="group in groupList" :key="group.id" :index="String(group.id)">
              <i class="el-icon-folder"></i>
              <span slot="title">{{ group.name }}</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main>
          <!-- 多选状态提示 -->
          <div v-if="isMultiSelect && selectedMaterials.length > 0" class="selected-materials-info">
            <el-alert :title="`已选择 ${selectedMaterials.length} 个${type === 1 ? '图片' : type === 2 ? '视频' : '素材'}`"
              type="info" :closable="false" show-icon>
            </el-alert>
          </div>

          <el-table v-loading="isLoading" ref="singleTable" tooltip-effect="dark" @select="selectChange"
            @row-click="!isMultiSelect ? selectChange : null" @select-all="selectAllChange" :data="materialdDataList"
            style="width: 100%" border>
            <el-table-column type="selection" width="40"> </el-table-column>
            <el-table-column prop="name" :label="$t('material.table.name')" width="180"
              align="center"></el-table-column>
            <el-table-column prop="type" :label="$t('material.table.type')" align="center">
              <template slot-scope="scope">
                {{
                  scope.row.type === 1
                    ? $t("template.dialog.materialType.image")
                    : scope.row.type === 2
                      ? $t("template.dialog.materialType.video")
                      : $t("template.dialog.materialType.file")
                }}
              </template>
            </el-table-column>
            <el-table-column prop="path" :label="$t('material.table.preview')" align="center">
              <template slot-scope="scope">
                <el-image v-if="scope.row.path && scope.row.type == 1" :src="imageUrl + scope.row.path" class="img"
                  :preview-src-list="[imageUrl + scope.row.path]" fit="cover"></el-image>
                <video class="img" v-else :src="imageUrl + scope.row.path" controls></video>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" :label="$t('template.table.created_at')" align="center">
              <template slot-scope="scope">
                {{ $formatTimeStamp(scope.row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-main>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowMaterial = false">{{
          $t("public.cancel")
          }}</el-button>
        <el-button type="primary" @click="confirmMaterial" :disabled="selectedMaterials.length === 0">
          {{ isMultiSelect ?
            `确认添加 (${selectedMaterials.length})` :
            $t("public.confirm")
          }}
        </el-button>
      </span>
    </el-dialog>
    <!-- 新增工作模板弹窗 -->
    <el-dialog :title="isEditWorkTemplate
      ? $t('template.button.updateWorkTemplate')
      : $t('template.button.createWorkTemplate')
      " :visible.sync="isShowWorkTemplate" style="margin-top: 300px" width="20%" @close="handleWorkTemplateClose">
      <el-form :model="workTemplateForm" :rules="workTemplateRules" ref="workTemplateFormRef" label-width="100px"
        class="demo-ruleForm">
        <el-form-item :label="$t('template.form.name')" prop="name" required>
          <el-input v-model="workTemplateForm.name" :placeholder="$t('template.form.namePlaceholder')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('template.dialog.title.material')" required>
          <el-button type="primary" @click="selectFile" style="width: 50%">
            {{ $t("template.dialog.title.material") }}
          </el-button>
          <div class="selected-material-count">
            {{ $t("template.dialog.title.material") }}：{{ selectedRows.name || selectedRows.sm_name || " " }}
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowWorkTemplate = false">
          {{ $t("public.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleSaveWorkTemplate">
          {{ $t("public.confirm") }}
        </el-button>
      </span>
    </el-dialog>


    <!-- iframe填写弹窗 -->
    <el-dialog :visible.sync="isShowIframe" style="padding-top:300px" width="40%">
      <el-form :model="iframeeForm" ref="iframeRef" label-width="120px" class="iframe-dialog-form">
        <el-form-item :label="$t('template.form.iframeUrl')" prop="url" required>
          <el-input v-model="iframeeForm.url" :placeholder="$t('template.form.urlPlaceholder')"></el-input>
        </el-form-item>

      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowIframe = false">
          {{ $t("public.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleIframeTemplate">
          {{ $t("public.confirm") }}
        </el-button>
      </span>
    </el-dialog>



    <!-- 打包弹窗 -->
    <el-dialog :title="$t('template.dialog.title.pack')" style="margin-top: 100px" :visible.sync="isShowPack"
      width="30%" @close="
        packForm.resource_pack_name = '';
      packForm.name = '';
      ">
      <el-form :model="packForm" :rules="packRule" style="padding: 20px" ref="packForm" label-width="100px"
        label-position="left" class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('template.form.resourcePackName')" prop="resource_pack_name">
              <el-input v-model="packForm.resource_pack_name"
                :placeholder="$t('template.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('template.form.resourcePackAlias')" prop="name">
              <el-input v-model="packForm.name"
                :placeholder="$t('template.form.resourcePackAliasPlaceholder')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowPack = false">{{
          $t("public.cancel")
          }}</el-button>
        <el-button type="primary" @click="confirmPack()">{{
          $t("public.confirm")
          }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TemplatePropertiesPanel from "@/components/TemplatePropertiesPanel.vue";
import CommonMaterialsPanel from "@/components/CommonMaterialsPanel.vue";
import TemplateArea from "@/components/TemplateArea.vue";
import ResizableSplitter from "@/components/ResizableSplitter.vue";
import { zipPack } from "@/utils/zipPack";
import {
  getList,
  getMaterialList,
  getDetail,
  add,
  edit,
  del,
  savePack,
} from "@/api/template.js";
import { getAllMaterialGroups } from "@/api/material.js";

export default {
  components: { TemplatePropertiesPanel, CommonMaterialsPanel, TemplateArea, ResizableSplitter },
  data () {
    return {
      materialClientKey: 0,
      draggedMaterial: null, // Store dragged material
      dropTargetIndex: -1, // Store drop target index
      isShowIframe: false,

      // 面板宽度管理
      leftPanelWidth: 200, // 左侧面板宽度
      rightPanelWidth: 230, // 右侧面板宽度
      leftPanelMinWidth: 200,
      leftPanelMaxWidth: 500,
      // 画板刷新触发器
      templateAreaRefreshKey: 0,
      rightPanelMinWidth: 200,
      rightPanelMaxWidth: 450,

      isEditWorkTemplate: false, // 新增：工作模板编辑状态
      isMultiSelect: false, // 标记是否为多选模式
      selectedRows: [], // 存储多选结果
      selectedMaterials: [], // 新增：存储当前选中的多个素材
      isShowWorkTemplate: false,
      workTemplateForm: {
        name: "", // 模板名称
      },
      iframeeForm: {
        name: "", // 模板名称
      },
      workTemplateRules: {
        name: [{ required: true, message: this.$i18n.t("template.form.namePlaceholder"), trigger: "blur" }],
      },
      groupList: [],
      selectedGroupId: "0",
      highlightedMaterialIndex: -1, // 新增：高亮素材索引
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      pageNumM: 1,
      pageSizeM: 10,
      totalM: 0,
      // 在 data 中定义一个变量来跟踪当前页面的背景设置
      currentPageBackgroundSettings: null,
      form: {
        name: "",
        date: [],
      },
      templatePages: [
        {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        },
      ],
      // 区域类型配置
      areaTypeConfigs: {
        1: { // 图片区域
          name: "图片区域",
          icon: "el-icon-picture-outline",
          defaultWidth: 200,
          defaultHeight: 200,
          minWidth: 50,
          minHeight: 50,
          lockAspectRatio: true,
          resizable: true,
          draggable: true,
          backgroundColor: "rgba(64, 158, 255, 0.1)",
          border: "2px dashed #409eff"
        },
        2: { // 视频区域
          name: "视频区域",
          icon: "el-icon-video-camera",
          defaultWidth: 200,
          defaultHeight: 200,
          minWidth: 100,
          minHeight: 100,
          lockAspectRatio: false,
          resizable: true,
          draggable: true,
          backgroundColor: "rgba(103, 194, 58, 0.1)",
          border: "2px dashed #67c23a"
        },
        5: { // 网页区域
          name: "网页区域",
          icon: "el-icon-link",
          defaultWidth: 200,
          defaultHeight: 200,
          minWidth: 100,
          minHeight: 100,
          lockAspectRatio: false,
          resizable: true,
          draggable: true,
          backgroundColor: "rgba(144, 147, 153, 0.1)",
          border: "2px dashed #909399"
        }
      },
      currentPage: 0,
      sharedAddForm: {
        name: "",
        resolution_ratio: "1920x1080",
        swipter_time: -1,
      },
      packForm: {
        resource_pack_name: "",
        name: "",
        html_content: "",
      },
      resolutionRatioList: [
        {
          id: 0,
          name: "1920x1080",
        },
        {
          id: 1,
          name: "1080x1920",
        },
        {
          id: 2,
          name: "1440x900",
        },
        {
          id: 3,
          name: "1280x720",
        }
      ],
      rules: {
        name: [
          {
            required: true,
            message: this.$i18n.t("template.form.namePlaceholder"),
            trigger: "blur",
          },
        ],
        resolution_ratio: [
          {
            required: true,
            message: this.$i18n.t("template.form.resolutionRatioPlaceholder"),
            trigger: "change",
          },
        ],
      },
      packRule: {
        name: [
          {
            required: false,
            message: this.$i18n.t("template.form.resourcePackAliasPlaceholder"),
            trigger: "blur",
          },
        ],
        resource_pack_name: [
          {
            required: true,
            message: this.$i18n.t("template.form.resourcePackNamePlaceholder"),
            trigger: "blur",
          },
        ],
      },
      isLoading: false,
      isShowTemplate: false,
      isShowMaterial: false,
      isShowPack: false,
      isEdit: false,
      type: 1,
      selectedRow: null,
      templateId: "",
      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
      materialdDataList: [],
    };
  },
  computed: {
    canvasDimensions () {
      const ratio = this.sharedAddForm.resolution_ratio;
      if (!ratio || typeof ratio !== 'string') {
        return { width: 960, height: 540 }; // Default size
      }

      const parts = ratio.split('x');
      if (parts.length !== 2) {
        return { width: 960, height: 540 }; // Default size on parse error
      }
      const width = parseInt(parts[0], 10);
      const height = parseInt(parts[1], 10);


      if (isNaN(width) || isNaN(height)) {
        return { width: 960, height: 540 }; // Default size on NaN
      }

      console.log(`width:${Math.round(width / 2)}||height:${Math.round(height / 2)}`);

      // According to the requirement, the ratio is 2:1 (e.g., 1920x1080 -> 960x540)
      return { width: Math.round(width / 2), height: Math.round(height / 2) };
    }
  },
  created () {
    this.getList();
    this.loadGroupList();
    this.currentPageBackgroundSettings =
      this.templatePages[this.currentPage].backgroundSettings;

    // 加载保存的面板宽度设置
    this.loadPanelWidths();
  },
  methods: {
    async loadGroupList () {
      try {
        const res = await getAllMaterialGroups();
        if (res.code === 0) {
          this.groupList = res.data || [];
        }
      } catch (error) {
        console.error("Failed to load material groups:", error);
      }
    },

    getMaterialNameById (materialId) {
      const material = this.materialdDataList.find(
        (item) => item.id === materialId
      );
      return material ? material.name : "未知素材";
    },
    handleGroupSelect (index) {
      let type = this.type;
      if (this.type == 4) {
        type = 1;
      }

      this.selectedGroupId = index;
      this.getMaterialList(type);
    },
    removeSelectedMaterial (materialId) {
      this.workTemplateForm.selectedMaterials =
        this.workTemplateForm.selectedMaterials.filter(
          (id) => id !== materialId
        );
    },
    getList () {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        created_at_start:
          this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code === 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
          this.isLoading = false;
        }
      });
    },
    isLock (type) {
      if (type === 5) {
        return false;
      } else {
        return true;
      }
    },
    handleIframeTemplate () {
      this.iframeeForm.url
      this.templatePages[this.currentPage].materialList.push({
        clientKey: this.materialClientKey++,
        type: 5,
        url: this.iframeeForm.url,
        template_sm_type: 5,
        source_width: 500,
        source_height: 300,
      })
      this.isShowIframe = false;
    },
    // 新增：打开工作模板弹窗
    addwork () {
      //  this.$refs.workTemplateFormRef.resetFields();
      this.isShowWorkTemplate = true;
      this.isMultiSelect = true; // 启用多选模式
      // 重置表单
      // this.workTemplateForm.name = "";
    },

    // 处理卡片操作
    handleCommand (command, item) {
      if (command === 'edit') {
        if (item.type === 1) {
          this.edit(item);
        } else {
          this.editWorkTemplate(item);
        }
      } else if (command === 'pack') {
        this.zipPackage(item.id, item.type);
      } else if (command === 'delete') {
        this.confirmDelete(item.id);
      }
    },

    // 确认删除
    confirmDelete (id) {
      this.$confirm('确定要删除这个模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.del(id);
      }).catch(() => { });
    },

    // 获取素材图标
    getMaterialIcon (item) {
      if (item.template_sm_type === 2) return 'el-icon-time';
      if (item.template_sm_type === 5) return 'el-icon-link';
      if (item.type === 1) return 'el-icon-picture-outline';
      if (item.type === 2) return 'el-icon-video-camera';
      return 'el-icon-document';
    },

    // 获取素材名称
    getMaterialName (item) {
      if (item.template_sm_type === 2) return '时间组件';
      if (item.template_sm_type === 5) return '网页组件';
      return item.sm_name || '未命名素材';
    },

    handleSaveWorkTemplate () {
      this.$refs.workTemplateFormRef.validate((valid) => {
        if (valid) {
          const api = this.isEditWorkTemplate ? edit : add;
          const params = {
            name: this.workTemplateForm.name,
            type: 2,
            template_sm: this.workTemplateForm.template_sm,
          };

          // 编辑时传递 ID，新建时不需要
          const request = this.isEditWorkTemplate
            ? api(params, this.id)
            : api(params);

          request.then(() => {
            this.$message.success(
              this.isEditWorkTemplate
                ? this.$t("public.editSuccess")
                : this.$t("public.addSuccess")
            );
            this.isShowWorkTemplate = false;
            this.getList(); // 刷新列表
          });
        }
      });
    },
    editWorkTemplate (row) {
      this.id = row.id;
      this.isEditWorkTemplate = true; // 标记为编辑模式
      this.isShowWorkTemplate = true; // 打开工作模板窗口

      // 获取工作模板详情
      getDetail({ id: row.id, type: 2 }).then((res) => {
        if (res.code === 0) {
          // 填充表单数据
          this.workTemplateForm.name = res.data.name;

          // 处理已选素材（根据实际数据结构调整）
          if (res.data.template_sm && res.data.template_sm.length > 0) {
            this.workTemplateForm.template_sm = res.data.template_sm;
            this.selectedRows = res.data.template_sm[0]; // 假设第一个为选中素材
          }
          console.log("dddd", this.selectedRows);
        }
      });
    },
    // 新增：关闭弹窗时的重置
    handleWorkTemplateClose () {
      this.isShowWorkTemplate = false;
      this.workTemplateForm.name = "";
      this.workTemplateForm = { name: "" }; // 清空表单
      this.selectedRows = []; // 清空选中素材
      this.selectedMaterials = []; // 清空多选素材
    },

    getMaterialList (type) {
      getMaterialList({
        page: this.pageNumM,
        pageSize: this.pageSizeM,
        name: this.form.name,
        type: type,
        group_id: this.selectedGroupId === "0" ? null : this.selectedGroupId,
      }).then((res) => {
        if (res.code === 0) {
          this.materialdDataList = res.data.data;
          this.totalM = res.data.total;
        }
      });
    },
    clearBackground () {
      this.templatePages[this.currentPage].backgroundUrl = "";
      this.templatePages[this.currentPage].backgroundList = [];
    },

    // 在 getDetail 方法中更新 currentPageBackgroundSettings
    async getDetail (id, type) {
      if (type === 1) {
        return new Promise((resolve) => {
          getDetail({
            id,
          }).then((res) => {
            if (res.code === 0) {
              resolve(res.data.template_sm);
            }
          });
        });
      } else {
        getDetail({
          id,
          type: 2,
        }).then((res) => {
          if (res.code === 0) {
            this.sharedAddForm.name = res.data.name;
            this.sharedAddForm.resolution_ratio = res.data.resolution_ratio;
            this.sharedAddForm.swipter_time = res.data.swipter_time || -1;

            // 清空现有页面
            this.templatePages = [];

            res.data.template_sm.forEach((item) => {
              try {
                // 处理字段名映射：将 API 返回的 multi_files 转换为前端使用的 multiFiles
                if (item.multi_files !== undefined) {
                  this.$set(item, 'multiFiles', item.multi_files);
                  delete item.multi_files;
                }

                // 如果 multiFiles 存在且为数组，进行数据验证和处理
                if (item.multiFiles && Array.isArray(item.multiFiles)) {
                  item.multiFiles.forEach(file => {
                    // 确保每个文件都有必要的字段
                    if (!file.clientKey) {
                      file.clientKey = this.materialClientKey++;
                    }
                    // 设置默认间隔时间
                    if (!file.interval_time) {
                      file.interval_time = 5;
                    }
                    // 确保其他必要字段存在
                    if (!file.path) {
                      console.warn('多文件缺少路径信息:', file);
                      file.path = '';
                    }
                    if (!file.sm_name) {
                      file.sm_name = '未命名文件';
                    }
                  });
                } else if (item.multiFiles === null || item.multiFiles === undefined) {
                  // 如果 multi_files 为 null 或 undefined，设置为空数组
                  item.multiFiles = [];
                } else if (typeof item.multiFiles === 'string') {
                  // 如果是字符串，尝试解析为数组
                  try {
                    item.multiFiles = JSON.parse(item.multiFiles);
                  } catch (parseError) {
                    console.error('解析 multiFiles 字符串失败:', parseError, item.multiFiles);
                    item.multiFiles = [];
                  }
                }

                // 验证其他必要字段
                if (!item.path && item.multiFiles && item.multiFiles.length > 0) {
                  // 如果 path 为空但有 multiFiles，则将 path 设置为第一个文件的路径
                  this.$set(item, 'path', item.multiFiles[0].path);
                } else if (!item.path) {
                  item.path = '';
                }
                if (!item.sm_name) {
                  item.sm_name = '未命名素材';
                }

                // 纠正错误的 type
                if (item.type === 0 && item.multiFiles && item.multiFiles.length > 0) {
                  this.$set(item, 'type', item.multiFiles[0].type);
                }
              } catch (error) {
                console.error('处理模板素材数据时出错:', error, item);
                // 继续处理下一个项目，不中断整个流程
              }

              item.clientKey = this.materialClientKey++;
              const pageIndex = item.template_page - 1;

              // 确保页面存在并初始化所有属性
              if (!this.templatePages[pageIndex]) {
                this.templatePages[pageIndex] = {
                  backgroundUrl: "",
                  materialList: [],
                  backgroundList: [],
                  backgroundSettings: {
                    backgroundSize: "cover", // 默认值
                    backgroundPosition: "center center",
                  },
                };
              }

              // 处理素材
              if (item.template_sm_type === 3) {
                // 恢复背景图和背景设置
                this.templatePages[pageIndex].backgroundList.push(item);
                this.templatePages[pageIndex].backgroundUrl = item.path;

                // 如果后端有保存背景显示设置，则恢复
                if (item.background_display) {
                  this.templatePages[
                    pageIndex
                  ].backgroundSettings.backgroundSize = item.background_display;
                }
              } else {
                this.templatePages[pageIndex].materialList.push(item);
              }
            });

            // 修复稀疏数组
            for (let i = 0; i < this.templatePages.length; i++) {
              if (!this.templatePages[i]) {
                this.templatePages[i] = {
                  backgroundUrl: "",
                  materialList: [],
                  backgroundList: [],
                  backgroundSettings: {
                    backgroundSize: "cover",
                    backgroundPosition: "center center",
                  },
                };
              }
            }

            // 如果 templatePages 仍然为空，添加一个默认页面
            if (this.templatePages.length === 0) {
              this.templatePages.push({
                backgroundUrl: "",
                materialList: [],
                backgroundList: [],
                backgroundSettings: {
                  backgroundSize: "cover",
                  backgroundPosition: "center center",
                },
              });
            }

            // 确保 currentPage 是有效的
            if (this.currentPage >= this.templatePages.length) {
              this.currentPage = 0;
            }

            // 更新 currentPageBackgroundSettings
            this.currentPageBackgroundSettings =
              this.templatePages[this.currentPage].backgroundSettings;
          }
        });
      }
    },

    // 新建模板
    add () {
      this.close();
      this.isShowTemplate = true;
    },
    edit (row) {
      if (row.type === 1) {
        this.id = row.id;
        this.getDetail(row.id);
        this.isEdit = true;
        this.isShowTemplate = true;

      }
    },
    del (id) {
      del(id).then((res) => {
        this.$message({
          type: "success",
          message: this.$i18n.t("public.deleteSuccess"),
        });
        this.getList();
      });
    },
    close () {
      this.templatePages = [
        {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        },
      ];
      this.currentPage = 0;
      this.sharedAddForm.name = "";
      this.sharedAddForm.resolution_ratio = "1920x1080";
      this.sharedAddForm.swipter_time = -1;
      this.isEdit = false;
    },
    close1 () {
      // 关闭素材选择弹窗时重置选择状态
      this.selectedMaterials = [];
      this.selectedRows = [];
      this.selectedRow = null;
      this.isMultiSelect = false;
    },
    // 清空重置模板
    clearTemplate () {
      this.templatePages[this.currentPage].backgroundList = [];
      this.templatePages[this.currentPage].materialList = [];
    },
    // 搜索
    searchForm () {
      this.getList();
    },
    // 重置
    resetForm () {
      this.pageNum = 1;
      this.$refs.form.resetFields();
      this.getList();
    },
    handleSizeChange (val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange (val) {
      this.pageNum = val;
      this.getList();
    },
    // 单选框选中的数据
    // 重写选择事件处理
    selectChange (selection) {
      console.log(selection, "当前选中的素材列表");

      if (this.isMultiSelect) {
        // 多选模式：存储所有选中项
        this.selectedMaterials = selection;
        this.selectedRows = selection; // 保持兼容性
      } else {
        // 单选模式：只允许选择一个
        if (selection.length > 1) {
          const del_row = selection.shift();
          this.$refs.singleTable.toggleRowSelection(del_row, false);
        }
        this.selectedRow = selection[0];
        this.selectedMaterials = selection.length > 0 ? [selection[0]] : [];
      }
    },

    // 新增：处理全选事件
    selectAllChange (selection) {
      if (this.isMultiSelect) {
        this.selectedMaterials = selection;
        this.selectedRows = selection; // 保持兼容性
      }
    },
    // 删除素材
    delMaterial (index) {
      try {
        if (this.templatePages[this.currentPage]?.materialList?.[index]) {
          // 使用Vue.set确保响应式更新
          const currentMaterialList = [...this.templatePages[this.currentPage].materialList];
          currentMaterialList.splice(index, 1);

          // 更新其余素材的 template_index
          this.updateTemplateIndices(currentMaterialList);

          // 更新整个materialList以确保响应式更新
          this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

          // 清除高亮状态
          this.highlightedMaterialIndex = -1;

          this.$message({
            type: 'success',
            message: this.$i18n.t('public.deleteSuccess')
          });

          // 确保属性面板也能及时更新
          this.$nextTick(() => {
            if (this.$refs.propertiesPanel) {
              // 调用属性面板的刷新方法
              if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
                this.$refs.propertiesPanel.refreshCurrentMaterialData();
              } else {
                this.$refs.propertiesPanel.$forceUpdate();
              }
            }
          });

          // 再次强制更新当前页面，确保所有相关组件都更新
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        } else {
          this.$message({
            type: 'warning',
            message: this.$i18n.t('template.error.materialNotFound')
          });
        }
      } catch (error) {
        console.error('删除素材失败:', error);
        this.$message({
          type: 'error',
          message: this.$i18n.t('public.deleteFailed')
        });
      }
    },
    // 重构 addMaterial 方法 - 直接创建空区域
    addMaterial (type) {
      this.type = type;

      // 确保templatePages和当前页已初始化
      if (!this.templatePages) {
        this.templatePages = [{
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        }];
      }
      if (!this.templatePages[this.currentPage]) {
        this.templatePages[this.currentPage] = {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        };
      }
      if (!this.templatePages[this.currentPage].materialList) {
        this.templatePages[this.currentPage].materialList = [];
      }

      // 根据类型直接创建区域
      if (type === 1) {
        // 图片区域
        this.addImageArea();
      } else if (type === 2) {
        // 视频区域
        this.addVideoArea();
      } else if (type === 3) {
        // 时间组件
        this.addDateTime();
      } else if (type === 4) {
        // 背景图：保持原有逻辑，需要选择素材
        this.addBackground();
      } else if (type === 5) {
        // 网页区域
        this.addWebArea();
      }
    },
    //获取弹窗
    selectFile () {
      // 重置选择状态
      this.selectedMaterials = [];
      this.selectedRows = [];
      this.selectedRow = null;
      // 工作模板使用单选模式
      this.isMultiSelect = false;
      this.getMaterialList(3);
      this.isShowMaterial = true;
      console.log(this.isShowMaterial);
    },

    // 创建图片区域
    addImageArea () {
      const config = this.areaTypeConfigs[1];
      const offset = this.templatePages[this.currentPage].materialList.length * 20; // 错开位置

      const newMaterial = {
        clientKey: this.materialClientKey++,
        type: 1,
        template_sm_type: 1,
        path: "",
        sm_id: 0,
        sm_name: config.name,
        source_width: config.defaultWidth,
        source_height: config.defaultHeight,
        x_axis: 50 + offset, // 默认位置，避免重叠
        y_axis: 50 + offset,
        width: config.defaultWidth,
        height: config.defaultHeight,
        template_page: this.currentPage + 1,
        areaConfig: config // 附加配置信息
      };

      const currentMaterialList = [...this.templatePages[this.currentPage].materialList];
      currentMaterialList.push(newMaterial);
      this.updateTemplateIndices(currentMaterialList);
      this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

      this.templateAreaRefreshKey++;
      this.$message.success(`${config.name}已添加`);
    },

    // 创建视频区域
    addVideoArea () {
      const config = this.areaTypeConfigs[2];
      const offset = this.templatePages[this.currentPage].materialList.length * 20; // 错开位置

      const newMaterial = {
        clientKey: this.materialClientKey++,
        type: 2,
        template_sm_type: 1,
        path: "",
        sm_id: 0,
        sm_name: config.name,
        source_width: config.defaultWidth,
        source_height: config.defaultHeight,
        x_axis: 50 + offset,
        y_axis: 50 + offset,
        width: config.defaultWidth,
        height: config.defaultHeight,
        template_page: this.currentPage + 1,
        areaConfig: config // 附加配置信息
      };

      const currentMaterialList = [...this.templatePages[this.currentPage].materialList];
      currentMaterialList.push(newMaterial);
      this.updateTemplateIndices(currentMaterialList);
      this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

      this.templateAreaRefreshKey++;
      this.$message.success(`${config.name}已添加`);
    },

    // 创建网页区域
    addWebArea () {
      const config = this.areaTypeConfigs[5];
      const offset = this.templatePages[this.currentPage].materialList.length * 20; // 错开位置

      const newMaterial = {
        clientKey: this.materialClientKey++,
        type: 5,
        template_sm_type: 5,
        url: "",
        sm_id: 0,
        sm_name: config.name,
        source_width: config.defaultWidth,
        source_height: config.defaultHeight,
        x_axis: 50 + offset,
        y_axis: 50 + offset,
        width: config.defaultWidth,
        height: config.defaultHeight,
        template_page: this.currentPage + 1,
        areaConfig: config // 附加配置信息
      };

      const currentMaterialList = [...this.templatePages[this.currentPage].materialList];
      currentMaterialList.push(newMaterial);
      this.updateTemplateIndices(currentMaterialList);
      this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

      this.templateAreaRefreshKey++;
      this.$message.success(`${config.name}已添加`);
    },

    // 处理背景图选择（保持原有逻辑）
    addBackground () {
      this.loadGroupList();
      this.isMultiSelect = false;
      this.selectedMaterials = [];
      this.selectedRows = [];
      this.selectedRow = null;
      this.getMaterialList(1);
      this.isShowMaterial = true;
    },

    // 修改 confirmMaterial 方法
    // 在 el-select 的 change 事件中更新 currentPageBackgroundSettings
    handleBackgroundSizeChange (newValue) {
      this.templatePages[this.currentPage].backgroundSettings.backgroundSize =
        newValue;
      this.currentPageBackgroundSettings =
        this.templatePages[this.currentPage].backgroundSettings;
    },

    addDateTime () {
      console.log("addDateTime");

      console.log(this.templatePages[this.currentPage], "this.templatePages[this.currentPage]this.templatePages[this.currentPage]");

      // 创建新的时间组件素材
      const newMaterial = {
        clientKey: this.materialClientKey++,
        template_id: 0,
        sm_id: 0,
        width: 240,
        height: 50,
        source_width: 240,
        source_height: 50,
        x_axis: 0,
        y_axis: 0,
        sm_name: "时间组件",
        path: "",
        type: 0,
        template_sm_type: 2,
        template_page: this.currentPage + 1,
        background_display: ""
      };

      // 使用响应式更新方式添加素材
      const currentMaterialList = [...this.templatePages[this.currentPage].materialList];
      currentMaterialList.push(newMaterial);
      this.updateTemplateIndices(currentMaterialList);
      this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

      this.templateAreaRefreshKey++;

      // 显示成功消息
      this.$message.success('时间组件添加成功');

      // 确保属性面板也能及时更新
      this.$nextTick(() => {
        if (this.$refs.propertiesPanel) {
          // 调用属性面板的刷新方法
          if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
            this.$refs.propertiesPanel.refreshCurrentMaterialData();
          } else {
            this.$refs.propertiesPanel.$forceUpdate();
          }
        }
      });
    },



    confirmMaterial () {
      console.log(this.type, "this.type");

      // 确保templatePages和当前页已初始化
      if (!this.templatePages) {
        this.templatePages = [{
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        }];
      }
      if (!this.templatePages[this.currentPage]) {
        this.templatePages[this.currentPage] = {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        };
      }
      if (!this.templatePages[this.currentPage].materialList) {
        this.templatePages[this.currentPage].materialList = [];
      }
      if (!this.templatePages[this.currentPage].backgroundList) {
        this.templatePages[this.currentPage].backgroundList = [];
      }

      // 检查是否有选中的素材
      if (!this.selectedMaterials || this.selectedMaterials.length === 0) {
        this.$message.warning(this.$i18n.t("template.form.materialsPlaceholder"));
        return;
      }

      // 工作模板的特殊处理（保持原有逻辑）
      if (this.isShowWorkTemplate) {
        this.workTemplateForm.template_sm = [
          {
            type: 10,
            template_sm_type: 1,
            path: this.selectedMaterials[0].path,
            sm_id: this.selectedMaterials[0].id,
            sm_name: this.selectedMaterials[0].name,
            source_width: this.selectedMaterials[0].source_width,
            source_height: this.selectedMaterials[0].source_height,
            x_axis: 0,
            y_axis: 0,
            width: 0,
            height: 0,
            template_page: 1,
          },
        ];
        this.workTemplateForm.name = this.workTemplateForm.name || "";
        this.selectedRows = this.selectedMaterials[0]; // 保持兼容性
        this.isShowMaterial = false;
        return;
      }

      // 普通模板处理
      if (this.type === 4) {
        // 背景图：单选
        const selectedMaterial = this.selectedMaterials[0];
        this.templatePages[this.currentPage].backgroundUrl = selectedMaterial.path;
        this.templatePages[this.currentPage].backgroundList = [
          {
            type: selectedMaterial.type,
            template_sm_type: 3,
            path: selectedMaterial.path,
            sm_id: selectedMaterial.id,
            sm_name: selectedMaterial.name,
            x_axis: 0,
            y_axis: 0,
            width: 0,
            height: 0,
            template_page: this.currentPage + 1,
            background_display: this.templatePages[this.currentPage].backgroundSettings.backgroundSize,
          },
        ];
      } else {
        // 图片、视频等：支持多选
        const currentMaterialList = [...this.templatePages[this.currentPage].materialList];

        this.selectedMaterials.forEach((material, index) => {
          const newMaterial = {
            clientKey: this.materialClientKey++,
            type: material.type,
            template_sm_type: this.type === 3 ? 2 : 1,
            path: material.path,
            sm_id: material.id,
            sm_name: material.name,
            source_width: material.source_width,
            source_height: material.source_height,
            x_axis: index * 50, // 错开位置避免重叠
            y_axis: index * 50,
            width: 0,
            height: this.type === 3 ? 50 : 0,
            template_page: this.currentPage + 1,
          };

          currentMaterialList.push(newMaterial);
        });

        this.updateTemplateIndices(currentMaterialList);
        // 使用Vue.set确保响应式更新
        this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

        this.templateAreaRefreshKey++;

        // 显示成功消息
        this.$message.success(
          `成功添加 ${this.selectedMaterials.length} 个${this.type === 1 ? '图片' : this.type === 2 ? '视频' : '素材'
          }`
        );

        // 确保属性面板也能及时更新
        this.$nextTick(() => {
          if (this.$refs.propertiesPanel) {
            // 调用属性面板的刷新方法
            if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
              this.$refs.propertiesPanel.refreshCurrentMaterialData();
            } else {
              this.$refs.propertiesPanel.$forceUpdate();
            }
          }
        });
      }

      this.isShowMaterial = false;
    },

    highlightMaterial (index) {
      this.highlightedMaterialIndex = index;
    },

    moveLayer ({ index, direction }) {
      const currentMaterialList = [...this.templatePages[this.currentPage].materialList];

      if (direction === 'up') {
        if (index > 0) {
          // 交换元素位置
          [currentMaterialList[index], currentMaterialList[index - 1]] =
            [currentMaterialList[index - 1], currentMaterialList[index]];
        }
      } else if (direction === 'down') {
        if (index < currentMaterialList.length - 1) {
          // 交换元素位置
          [currentMaterialList[index], currentMaterialList[index + 1]] =
            [currentMaterialList[index + 1], currentMaterialList[index]];
        }
      }

      // 更新所有素材的 template_index
      this.updateTemplateIndices(currentMaterialList);

      // 更新整个materialList以确保响应式更新
      this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

      // 更新高亮索引
      if (direction === 'up' && index > 0) {
        this.highlightedMaterialIndex = index - 1;
      } else if (direction === 'down' && index < currentMaterialList.length - 1) {
        this.highlightedMaterialIndex = index + 1;
      }

      this.$message({
        type: 'success',
        message: '素材层级更新成功'
      });

      // 确保属性面板也能及时更新
      this.$nextTick(() => {
        if (this.$refs.propertiesPanel) {
          // 调用属性面板的刷新方法
          if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
            this.$refs.propertiesPanel.refreshCurrentMaterialData();
          } else {
            this.$refs.propertiesPanel.$forceUpdate();
          }
        }
      });

      // 再次强制更新当前页面，确保所有相关组件都更新
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    save () {
      this.$refs.propertiesPanel.validate((valid) => {
        if (valid) {
          // 准备所有素材数据，包括背景设置
          let allMaterials = [];
          this.templatePages.forEach((page, pageIndex) => {
            // 添加背景设置到背景素材项
            if (page.backgroundList && page.backgroundList.length > 0) {
              page.backgroundList.forEach((bgItem) => {
                // 为背景素材添加显示设置
                bgItem.background_display =
                  page.backgroundSettings.backgroundSize;
                // 记录所属页面
                bgItem.template_page = pageIndex + 1;
                allMaterials.push(bgItem);
              });
            }


            // 添加普通素材
            page.materialList.forEach((item) => {


              item.template_page = pageIndex + 1;
              allMaterials.push(item);
            });
          });

          if (this.isEdit) {
            // 编辑模板
            edit(
              {
                template_sm: allMaterials,
                resolution_ratio: this.sharedAddForm.resolution_ratio,
                canvas_ratio: `${this.canvasDimensions.width}x${this.canvasDimensions.height}`,
                swipter_time: this.sharedAddForm.swipter_time,
                name: this.sharedAddForm.name,
              },
              this.id
            ).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.editSuccess"),
              });
              this.isShowTemplate = false;
              this.getList();
            });
          } else {
            // 新增模板
            add({
              template_sm: allMaterials,
              resolution_ratio: this.sharedAddForm.resolution_ratio,
              canvas_ratio: `${this.canvasDimensions.width}x${this.canvasDimensions.height}`,
              name: this.sharedAddForm.name,
              swipter_time: this.sharedAddForm.swipter_time,
              type: 1,
            }).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.addSuccess"),
              });
              this.isShowTemplate = false;
              this.getList();
            });
          }
        }
      });
    },
    // 打包
    confirmPack () {
      this.$refs.packForm.validate((valid) => {
        if (valid) {
          savePack({
            html_content: this.html_content,
            resource_pack_name: this.packForm.resource_pack_name,
            name: this.packForm.name,
            id: this.templateId,
            type: this.type
          }).then((res) => {
            this.$message({
              type: "success",
              message: this.$i18n.t("template.form.successTips"),
            });
            this.$refs.packForm.resetFields();
            this.isShowPack = false;
            this.getList();
          });
        }
      });
      this.templateId = "",
        this.type = 1
    },
    // 生成打包 html 资源代码

    zipPackage (id, type) {

      let templateData = {};
      let templatePages = {};
      let swipterTime = 0
      let templateName = ""
      this.templateId = id
      this.type = type
      let pageSize = ""
      let canvasSize = ""
      getDetail({ id: id, type: type }).then((res) => {
        if (res.code === 0) {
          templateData = res.data;
        }
        console.log(templateData, "templateDatatemplateDatatemplateData");

        pageSize = templateData.resolution_ratio
        canvasSize = templateData.canvas_ratio
        templateName = templateData.name
        swipterTime = templateData.swipter_time

        // 遍历每一页
        templateData.template_sm.map((page) => {

          if (page?.template_page && templatePages) {
            const key = `page-${page.template_page}`
            templatePages[key] = templatePages[key] || []
            templatePages[key].push(page)
          }


        })

        this.html_content = zipPack(templateName, templatePages, swipterTime, pageSize, type, canvasSize)
      })

      this.isShowPack = true;

    },

    addNewPage () {
      this.templatePages.push({
        backgroundUrl: "",
        materialList: [],
        backgroundList: [],
        backgroundSettings: {
          backgroundSize: "cover",
          backgroundPosition: "center center",
        },
      });
      this.currentPage = this.templatePages.length - 1;
      this.currentPageBackgroundSettings =
        this.templatePages[this.currentPage].backgroundSettings; // 添加这行

      // 清除高亮状态
      this.highlightedMaterialIndex = -1;

      // 确保属性面板更新
      this.$nextTick(() => {
        if (this.$refs.propertiesPanel) {
          // 调用属性面板的刷新方法
          if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
            this.$refs.propertiesPanel.refreshCurrentMaterialData();
          } else {
            this.$refs.propertiesPanel.$forceUpdate();
          }
        }
      });
    },
    prevPage () {
      if (this.currentPage > 0) {
        this.currentPage--;
        this.currentPageBackgroundSettings =
          this.templatePages[this.currentPage].backgroundSettings;

        // 清除高亮状态
        this.highlightedMaterialIndex = -1;

        // 确保属性面板更新
        this.$nextTick(() => {
          if (this.$refs.propertiesPanel) {
            // 调用属性面板的刷新方法
            if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
              this.$refs.propertiesPanel.refreshCurrentMaterialData();
            } else {
              this.$refs.propertiesPanel.$forceUpdate();
            }
          }
        });
      }
    },
    nextPage () {
      if (this.currentPage < this.templatePages.length - 1) {
        this.currentPage++;
        this.currentPageBackgroundSettings =
          this.templatePages[this.currentPage].backgroundSettings;

        // 清除高亮状态
        this.highlightedMaterialIndex = -1;

        // 确保属性面板更新
        this.$nextTick(() => {
          if (this.$refs.propertiesPanel) {
            // 调用属性面板的刷新方法
            if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
              this.$refs.propertiesPanel.refreshCurrentMaterialData();
            } else {
              this.$refs.propertiesPanel.$forceUpdate();
            }
          }
        });
      }
    },
    delPage () {
      console.log(this.templatePages, this.currentPage);
      this.prevPage();
      if (this.templatePages.length > 1) {
        this.templatePages.pop(this.currentPage)
      }
    },

    // 更新间隔时间
    updateIntervalTime ({ material, interval_time }) {
      const index = this.templatePages[this.currentPage].materialList.findIndex(item => item.clientKey === material.clientKey);
      if (index !== -1) {
        this.$set(this.templatePages[this.currentPage].materialList[index], 'interval_time', interval_time);
      }
    },

    // 处理素材重新排序
    onMaterialsReordered (newOrder) {
      console.log('onMaterialsReordered called', newOrder);

      // 更新素材顺序
      const currentMaterials = [...this.templatePages[this.currentPage].materialList];
      const otherMaterials = currentMaterials.filter(material =>
        !(newOrder.some(ordered => ordered.clientKey === material.clientKey))
      );

      // 重新组织素材列表
      const reorderedList = [];
      newOrder.forEach(orderedMaterial => {
        const originalMaterial = currentMaterials.find(m => m.clientKey === orderedMaterial.clientKey);
        if (originalMaterial) {
          reorderedList.push(originalMaterial);
        }
      });

      // 添加其他素材
      otherMaterials.forEach(material => {
        reorderedList.push(material);
      });

      console.log('Reordered list:', reorderedList);

      // 使用 Vue 的响应式更新方法
      this.$set(this.templatePages[this.currentPage], 'materialList', reorderedList);

      // 强制更新视图
      this.$forceUpdate();

      // 额外确保属性面板也更新
      this.$nextTick(() => {
        if (this.$refs.propertiesPanel) {
          // 调用属性面板的刷新方法
          if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
            this.$refs.propertiesPanel.refreshCurrentMaterialData();
          } else {
            this.$refs.propertiesPanel.$forceUpdate();
          }
        }
        // 再次强制更新，确保所有相关组件都更新
        this.$forceUpdate();
      });

      console.log('Material list updated successfully');
    },

    // 重置轮播索引
    resetCarouselIndex (clientKey) {
      if (this.$refs.templateArea) {
        this.$refs.templateArea.resetCarouselIndex(clientKey);
      }
    },

    // 更新轮播时间间隔
    updateCarouselInterval (clientKey) {
      if (this.$refs.templateArea) {
        this.$refs.templateArea.updateCarouselInterval(clientKey);
      }
    },








    handleUpdateMultiFiles ({ clientKey, files }) {
      const material = this.templatePages[this.currentPage].materialList.find(
        (m) => m.clientKey === clientKey
      );
      if (material) {
        console.log('handleUpdateMultiFiles: 更新素材文件列表', clientKey, files.length);

        // 使用Vue.set确保响应式更新
        this.$set(material, 'multiFiles', files);

        // 触发画板刷新key，强制template-area组件重新渲染
        this.templateAreaRefreshKey++;
        console.log('handleUpdateMultiFiles: templateAreaRefreshKey updated to', this.templateAreaRefreshKey);

        // 强制更新画板上的素材显示
        this.$nextTick(() => {
          // 确保属性面板也立即更新
          if (this.$refs.propertiesPanel) {
            // 调用属性面板的刷新方法
            if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
              this.$refs.propertiesPanel.refreshCurrentMaterialData();
            } else {
              this.$refs.propertiesPanel.$forceUpdate();
            }
            console.log('handleUpdateMultiFiles: 属性面板已强制更新');
          }
          console.log('handleUpdateMultiFiles: 画板和属性面板已同步');
        });
      }
    },



    // 获取当前高亮区域
    getCurrentHighlightedMaterial () {
      if (this.highlightedMaterialIndex >= 0 && this.templatePages[this.currentPage]?.materialList) {
        return this.templatePages[this.currentPage].materialList[this.highlightedMaterialIndex];
      }
      return null;
    },

    // 左侧面板拖拽事件处理
    handleLeftPanelDrag (newWidth) {
      this.leftPanelWidth = newWidth;
    },

    handleLeftPanelDragStart (width) {
      console.log('开始拖拽左侧面板，当前宽度:', width);
    },

    handleLeftPanelDragEnd (width) {
      console.log('结束拖拽左侧面板，最终宽度:', width);
      // 可以在这里保存到本地存储
      localStorage.setItem('template-left-panel-width', width);
    },

    // 右侧面板拖拽事件处理
    handleRightPanelDrag (newWidth) {
      this.rightPanelWidth = newWidth;
    },

    handleRightPanelDragStart (width) {
      console.log('开始拖拽右侧面板，当前宽度:', width);
    },

    handleRightPanelDragEnd (width) {
      console.log('结束拖拽右侧面板，最终宽度:', width);
      // 保存到本地存储
      localStorage.setItem('template-right-panel-width', width);
    },

    // 更新素材属性
    handleUpdateMaterialProperty ({ clientKey, key, value }) {
      const material = this.templatePages[this.currentPage].materialList.find(
        (m) => m.clientKey === clientKey
      );
      if (material) {
        this.$set(material, key, value);
      }
    },

    // 加载面板宽度设置
    loadPanelWidths () {
      try {
        const savedLeftWidth = localStorage.getItem('template-left-panel-width');
        const savedRightWidth = localStorage.getItem('template-right-panel-width');

        if (savedLeftWidth) {
          const width = parseInt(savedLeftWidth);
          if (width >= this.leftPanelMinWidth && width <= this.leftPanelMaxWidth) {
            this.leftPanelWidth = width;
          }
        }

        if (savedRightWidth) {
          const width = parseInt(savedRightWidth);
          if (width >= this.rightPanelMinWidth && width <= this.rightPanelMaxWidth) {
            this.rightPanelWidth = width;
          }
        }
      } catch (error) {
        console.warn('加载面板宽度设置失败:', error);
      }
    },

    // 处理区域点击事件
    handleAreaClick (index) {
      console.log('handleAreaClick: 切换到区域', index);

      // 设置当前高亮区域索引
      this.highlightedMaterialIndex = index;

      // 强制更新属性面板，确保素材排序状态正确显示
      this.$nextTick(() => {
        if (this.$refs.propertiesPanel) {
          this.$refs.propertiesPanel.$forceUpdate();
          console.log('handleAreaClick: 属性面板已强制更新');

          // 如果属性面板有刷新方法，也调用一下
          if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
            this.$refs.propertiesPanel.refreshCurrentMaterialData();
          }
        }
        // 再次强制更新主组件
        this.$forceUpdate();
      });
    },

    handleCanvasClick () {
      this.highlightedMaterialIndex = -1;
    },

    updateTemplateIndices (materialList) {
      if (!materialList || !Array.isArray(materialList)) return;
      const len = materialList.length;
      materialList.forEach((item, i) => {
        this.$set(item, 'template_index', i + 1);
      });
    },

    // 拖拽相关方法
    handleDragging (left, top, index) {
      if (this.templatePages[this.currentPage]?.materialList?.[index]) {
        let material = this.templatePages[this.currentPage].materialList[index];
        let canvasWidth = this.canvasDimensions.width; // 画布宽度
        let canvasHeight = this.canvasDimensions.height; // 画布高度

        // 边界检测和限制
        let newX = Math.max(0, Math.min(left, canvasWidth - material.width));
        let newY = Math.max(0, Math.min(top, canvasHeight - material.height));
        console.log(canvasWidth, canvasHeight, material.width, material.height, newX, newY);

        // 自动吸附到网格
        const gridSize = 10;
        newX = Math.round(newX / gridSize) * gridSize;
        newY = Math.round(newY / gridSize) * gridSize;

        material.x_axis = newX;
        material.y_axis = newY;

        // 高亮当前拖拽的区域
        this.highlightedMaterialIndex = index;
      }
    },

    handleDragStart (index) {
      this.highlightedMaterialIndex = index;
      this.draggedMaterial = this.templatePages[this.currentPage].materialList[index];
      this.$message({
        message: '开始拖拽区域',
        type: 'info',
        duration: 1000
      });
    },

    handleDragEnd (index) {
      this.$nextTick(() => {
        // 立即清除高亮状态，提高响应性
        if (this.highlightedMaterialIndex === index) {
          this.highlightedMaterialIndex = -1;
        }
        this.draggedMaterial = null;
        this.dropTargetIndex = -1; // 清除放置目标高亮
      });
    },

    handleResizing (left, top, width, height, index) {
      if (this.templatePages[this.currentPage]?.materialList?.[index]) {
        const material = this.templatePages[this.currentPage].materialList[index];
        const canvasWidth = this.canvasDimensions.width; // 画布宽度
        const canvasHeight = this.canvasDimensions.height; // 画布高度

        // 获取区域配置
        const config = material.areaConfig || {};
        const minWidth = config.minWidth || 90;
        const minHeight = config.minHeight || 50;

        // 限制最小尺寸
        let newWidth = Math.max(minWidth, Math.round(width));
        let newHeight = Math.max(minHeight, Math.round(height));

        // 边界检测
        let newX = Math.max(0, Math.min(left, canvasWidth - newWidth));
        let newY = Math.max(0, Math.min(top, canvasHeight - newHeight));

        material.x_axis = newX;
        material.y_axis = newY;
        material.width = newWidth;
        material.height = newHeight;

        // 高亮当前缩放的区域
        this.highlightedMaterialIndex = index;
      }
    },

    handleResizeStart (index) {
      this.highlightedMaterialIndex = index;
      this.$message({
        message: '开始调整区域大小',
        type: 'info',
        duration: 1000
      });
    },

    handleResizeEnd (index) {
      this.$nextTick(() => {
        // 立即清除高亮状态，提高响应性
        if (this.highlightedMaterialIndex === index) {
          this.highlightedMaterialIndex = -1;
        }
      });
    },

    handleDragOver (event, index) {
      event.preventDefault();
      const targetArea = this.templatePages[this.currentPage].materialList[index];

      if (targetArea) {
        try {
          const draggedData = JSON.parse(event.dataTransfer.getData("application/json"));
          let canDrop = false;

          if (targetArea.areaConfig) {
            // 空区域检查
            const areaType = targetArea.areaConfig.name;
            if (areaType === '图片区域' && draggedData.type === 1) canDrop = true;
            else if (areaType === '视频区域' && draggedData.type === 2) canDrop = true;
            else if (areaType === '网页区域') canDrop = true;
          } else {
            // 非空区域检查
            canDrop = targetArea.type === draggedData.type;
          }

          if (canDrop) {
            this.dropTargetIndex = index;
          } else {
            this.dropTargetIndex = -1;
          }
        } catch (e) {
          this.dropTargetIndex = -1;
        }
      }
    },

    handleDrop (event, targetIndex) {
      event.preventDefault();
      try {
        const materialData = JSON.parse(event.dataTransfer.getData("application/json"));
        const targetArea = this.templatePages[this.currentPage].materialList[targetIndex];

        if (targetArea && materialData) {
          // 检查区域是否为空区域（有areaConfig表示是空区域）
          const isEmptyArea = targetArea.areaConfig;

          if (isEmptyArea) {
            // 空区域：检查素材类型是否匹配
            const areaType = targetArea.areaConfig.name;
            let isTypeMatch = false;

            if (areaType === '图片区域' && materialData.type === 1) {
              isTypeMatch = true;
            } else if (areaType === '视频区域' && materialData.type === 2) {
              isTypeMatch = true;
            } else if (areaType === '网页区域') {
              // 网页区域可以接受任何素材，或者有特殊处理逻辑
              isTypeMatch = true;
            }

            if (isTypeMatch) {
              // 填充空区域
              this.$set(targetArea, 'path', materialData.path);
              this.$set(targetArea, 'sm_name', materialData.name);
              this.$set(targetArea, 'sm_id', materialData.id);
              this.$set(targetArea, 'source_width', materialData.source_width);
              this.$set(targetArea, 'source_height', materialData.source_height);
              this.$set(targetArea, 'type', materialData.type);

              // 保持区域原始大小不变
              this.$set(targetArea, 'template_sm_type', 1);
              // 不调整区域的宽度和高度，保持原始尺寸

              this.$delete(targetArea, 'areaConfig');

              this.$message.success(`素材 "${materialData.name}" 已成功放置到${areaType}中`);
            } else {
              this.$message.error(`"${materialData.name}" 不能放置到${areaType}中，请选择匹配的素材类型`);
            }
          } else {
            // 非空区域：检查是否可以添加多个文件（图片区域和视频区域）
            if (targetArea.type === materialData.type) {
              // 检查是否是图片区域或视频区域，支持多个文件
              const areaType = targetArea.areaConfig ? targetArea.areaConfig.name : (targetArea.type === 1 ? '图片区域' : targetArea.type === 2 ? '视频区域' : '其他区域');

              if ((areaType === '图片区域' && materialData.type === 1) || (areaType === '视频区域' && materialData.type === 2)) {
                // 支持多个文件：将新素材添加到同一区域，形成轮播效果
                // 首先检查目标区域是否已经有多个文件
                const targetIndex = this.templatePages[this.currentPage].materialList.findIndex(item => item.clientKey === targetArea.clientKey);

                if (targetIndex !== -1) {
                  // 如果目标区域还没有多文件列表，创建一个
                  if (!targetArea.multiFiles) {
                    targetArea.multiFiles = [{
                      clientKey: targetArea.clientKey,
                      type: targetArea.type,
                      template_sm_type: 1,
                      path: targetArea.path,
                      sm_name: targetArea.sm_name,
                      sm_id: targetArea.sm_id,
                      source_width: targetArea.source_width,
                      source_height: targetArea.source_height,
                      interval_time: targetArea.interval_time || 5,
                    }];
                  }

                  // 添加新的素材到多文件列表
                  const newFile = {
                    clientKey: this.materialClientKey++,
                    type: materialData.type,
                    template_sm_type: 1,
                    path: materialData.path,
                    sm_name: materialData.name,
                    sm_id: materialData.id,
                    source_width: materialData.source_width,
                    source_height: materialData.source_height,
                    interval_time: 5, // 默认间隔时间5秒
                  };

                  targetArea.multiFiles.push(newFile);

                  // 更新目标区域的显示信息
                  this.$set(targetArea, 'path', materialData.path); // 显示最新添加的图片
                  this.$set(targetArea, 'sm_name', `${targetArea.multiFiles.length}个文件轮播`);
                  this.$set(targetArea, 'sm_id', materialData.id);

                  // 强制更新视图
                  this.$set(this.templatePages[this.currentPage].materialList, targetIndex, targetArea);

                  // 触发画板刷新key，强制template-area组件重新渲染
                  this.templateAreaRefreshKey++;
                  console.log('handleDrop: templateAreaRefreshKey updated to', this.templateAreaRefreshKey);

                  // 强制更新属性面板
                  this.$nextTick(() => {
                    if (this.$refs.propertiesPanel) {
                      this.$refs.propertiesPanel.refreshTrigger++;
                      console.log('handleDrop: 属性面板refreshTrigger已更新');
                    }
                  });

                  this.$message.success(`素材 "${materialData.name}" 已添加到${areaType}轮播（共${targetArea.multiFiles.length}个文件）`);
                }
              } else {
                // 不支持多个文件的区域：替换当前内容
                this.$set(targetArea, 'path', materialData.path);
                this.$set(targetArea, 'sm_name', materialData.name);
                this.$set(targetArea, 'sm_id', materialData.id);
                this.$set(targetArea, 'source_width', materialData.source_width);
                this.$set(targetArea, 'source_height', materialData.source_height);

                // 保持区域原始大小不变

                this.$message.success(`素材 "${materialData.name}" 已替换当前区域内容`);
              }
            } else {
              this.$message.error("素材类型与区域类型不匹配，无法替换");
            }
          }
        }
      } catch (e) {
        console.error("无法解析拖拽数据", e);
        this.$message.error("放置素材时发生错误");
      }
      this.dropTargetIndex = -1; // Reset drop target styling
    },

    isDropTarget (index) {
      if (this.dropTargetIndex !== index) return false;

      const targetArea = this.templatePages[this.currentPage].materialList[index];
      if (!targetArea) return false;

      // 如果是空区域，检查是否可以接受当前拖拽的素材
      if (targetArea.areaConfig) {
        try {
          const draggedData = JSON.parse(event?.dataTransfer?.getData("application/json") || '{}');
          const areaType = targetArea.areaConfig.name;

          if (areaType === '图片区域' && draggedData.type === 1) return 'valid';
          if (areaType === '视频区域' && draggedData.type === 2) return 'valid';
          if (areaType === '网页区域') return 'valid'; // 网页区域接受所有类型

          return 'invalid';
        } catch (e) {
          return false;
        }
      }

      // 如果是非空区域，检查类型匹配
      try {
        const draggedData = JSON.parse(event?.dataTransfer?.getData("application/json") || '{}');
        return targetArea.type === draggedData.type ? 'valid' : 'invalid';
      } catch (e) {
        return false;
      }
    },


  },
};
</script>

<style scoped lang="scss">
.template-page {
  height: calc(100vh - 84px);
  background-color: #f0f2f5;
}

.template-layout {
  height: 100%;
}

.content-main {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .actions-left {
    display: flex;
    gap: 12px;
  }

  .actions-right {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .search-input {
    width: 250px;
  }
}

.template-grid-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.template-card {
  margin-bottom: 8px;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.3);
  }

  .card-preview {
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;

    .template-icon {
      font-size: 48px;
      color: #fff;
    }
  }

  .card-info {
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 90px;

    .card-name {
      font-size: 14px;
      color: #303133;
      margin: 0 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 500;
    }

    .card-time {
      font-size: 12px;
      color: #909399;
      margin-top: auto;
    }

    .card-details {
      font-size: 12px;
      color: #606266;
      margin-bottom: 8px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;

      .type-tag {
        padding: 2px 8px;
        border-radius: 12px;
        color: #fff;
        font-weight: 500;
        font-size: 11px;

        &.type-1 {
          background-color: #409eff;
        }

        &.type-2 {
          background-color: #67c23a;
        }
      }
    }
  }

  .card-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;

    .el-dropdown-link {
      cursor: pointer;
      color: #fff;
      font-size: 18px;
      padding: 5px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);

      &:hover {
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  &:hover .card-actions {
    display: block;
  }
}

.pagination-footer {
  margin-top: 20px;
  text-align: right;
}

// 编辑弹窗样式
.template-dialog {
  &.is-fullscreen {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
      padding: 15px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }

  .template-toolbar {
    background: #f8f9fa;
    padding: 10px 12px;
    border-radius: 6px;
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;

    .tool-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
      padding: 8px 10px;
      background-color: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      min-width: 280px;
      align-items: flex-start;
      /* 左对齐 */

      .group-label {
        font-size: 12px;
        color: #303133;
        font-weight: 600;
        margin-bottom: 3px;
        text-align: left;
        width: 100%;
      }

      .area-buttons,
      .tool-controls {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
        /* 左对齐 */

        .area-btn {
          transition: all 0.2s ease;
          padding: 6px 12px;
          font-size: 12px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }

      .area-info {
        margin-top: 8px;

        .el-alert {
          padding: 8px 12px;
          width: 100%;
          /* 占满宽度 */

          .el-alert__title {
            font-size: 12px;
          }
        }
      }
    }

    .page-info {
      background: #f0f2f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: #606266;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
  }
}

.iframe-dialog-form {
  padding-top: 30px;
}

.flex {
  display: flex;
}

.img {
  width: 60px;
  height: 60px;
}

::v-deep .el-dialog {
  margin-top: 0 !important;
}

::v-deep .el-dialog__body {
  padding: 0 10px;
}

.container {
  display: flex;
  flex: 1;
  height: 100%;
  margin-top: 6px;
  gap: 0;
  overflow: hidden;
  align-items: flex-start;
  justify-content: flex-start;
}

.left-panel {
  height: 100%;
  background: transparent;
  flex-shrink: 0;
  overflow: hidden;
}

.middle-pane {
  flex: 1;
  height: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  display: flex;
  background: #000;
  overflow: auto;
  padding: 30px;

  .del {
    width: 24px;
    height: 24px;
    position: absolute;
    right: 4px;
    top: 4px;
    cursor: pointer;
    display: none;
    border: 1px solid #f0f0f0;
    border-radius: 100px;
    background-color: #8383835c;
  }
}

.draggable:hover .del {
  display: block;
}

.draggable.highlighted {
  outline: 2px solid #409EFF;
  box-shadow: 0 0 15px rgba(64, 158, 255, 0.6);
  transform: scale(1.02);
  // transition: all 0.2s ease;
  z-index: 1000;
}

.area-type-label {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  border-bottom-right-radius: 6px;
  z-index: 10;
}

.draggable {
  transition: all 0.2s ease;
  cursor: move;
}

.draggable:hover {
  outline: 1px solid #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.draggable:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.drop-target {
  outline: 3px dashed #67c23a !important;
  box-shadow: 0 0 20px rgba(103, 194, 58, 0.7) !important;
  transform: scale(1.05) !important;
  background-color: rgba(103, 194, 58, 0.1) !important;
}

.drop-target.invalid {
  outline: 3px dashed #f56c6c !important;
  box-shadow: 0 0 20px rgba(245, 108, 108, 0.7) !important;
  background-color: rgba(245, 108, 108, 0.1) !important;
}

.uniform-width {
  width: 80%;
}

.right-panel {
  height: 100%;
  background: transparent;
  flex-shrink: 0;
  overflow: hidden;
}

.view-item {
  margin-bottom: 10px;

  div {
    text-align: left;
  }
}

.thumbnail {
  width: 100%;
  height: auto;
  max-height: 100px;
  object-fit: cover;
}

.ml-large {
  margin-left: 20px;
}

.selected-materials-info {
  margin-bottom: 15px;
}
</style>

<style lang="scss">
.template-page {
  .el-card__body {
    padding: 0;
  }
}
</style>
